import { Router } from "express";
import { 
    setGoalsController, 
    getAllReviews, 
    updatePerformanceReview, 
    getEmployeePerformanceController
} from "../controllers/performance.controller.js";
import { apiLimiter } from "../middlewares/ratelimit.middleware.js";
import { requireAuth, permit } from "../middlewares/auth.middleware.js";


const router = Router();

router.post("/goals", 
    apiLimiter, 
    requireAuth,
    permit('admin', 'hr', 'manager'),
    setGoalsController
);

router.get("/reviews",  
    apiLimiter, 
    requireAuth,
    permit('admin', 'hr', 'manager'),
    getAllReviews
);

router.put("/review/:employeeId/:reviewId", 
    apiLimiter, 
    requireAuth,
    permit('admin', 'hr', 'manager'),
    updatePerformanceReview
);


router.get("/employee/:employeeId", 
    apiLimiter, 
    requireAuth,
    permit('admin', 'hr', 'manager', 'employee'),
    getEmployeePerformanceController
);

export default router;

