// controllers/auth.controller.js
import bcrypt from 'bcryptjs';
import { findOneUser, createUser } from '../dao/user.dao.js'
import { generateTokens } from "../utils/jwt.js";


// @desc Register User
export const registerUserController = async (req, res, next) => {
    try {
        const { username, email, password } = req.body;

        // check if user exists
        const userExists = await findOneUser({ email, username });
        if (userExists) return res.status(400).json({ message: 'User already exists', success: false });

        // hash password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Decide role based on email 
        // if (email.endsWith('@admin.com')) role = 'ADMIN';
        let role = 'employee';
        if (email.includes('admin')) role = 'admin';
        else if (email.includes('manager')) role = 'manager';
        else if (email.includes('hr')) role = 'hr';


        // create user
        const user = await new createUser({ username, email, password: hashedPassword, role });
        await user.save();
        // Generate tokens
        const { accessToken, refreshToken } = generateTokens(user);

        // Save refresh token in cookie
        user.refreshToken = refreshToken;
        res.cookie('refreshToken', refreshToken, { httpOnly: true });
        res.cookie('accessToken', accessToken, { httpOnly: true });
        await user.save();

        return res.status(201).json({
            message: 'User registered successfully',
            success: true,
            user: {
                id: user._id,
                name: user.username,
                email: user.email,
                role: user.role
            }
        });
    } catch (error) {
        next(error);
    }
};

// @desc Login User
export const loginUserController = async (req, res, next) => {
    try {
        const { username, email, password } = req.body;

        const user = await findOneUser({ $or: [{ username }, { email }] }).select('+password');
        if (!user) return res.status(401).json({ message: 'Invalid email or password', success: false });

        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) return res.status(401).json({ message: 'Invalid email or password' });

        // Generate tokens
        const { accessToken, refreshToken } = generateTokens(user);

        // Save refresh token in cookie
        user.refreshToken = refreshToken;
        user.lastLogin = new Date();
        await user.save();
        res.cookie('refreshToken', refreshToken, { httpOnly: true });
        res.cookie('accessToken', accessToken, { httpOnly: true });

        res.status(200).json({
            message: 'Login successful',
            success: true,
            user: {
                id: user._id,
                name: user.username,
                email: user.email,
                role: user.role
            }
        });
    } catch (error) {
        next(error);
    }
};



// @desc Logout User
export const logoutUserController = async (req, res, next) => {
    try {
        res.clearCookie('refreshToken');
        res.clearCookie('accessToken');

        res.json({ message: 'Logged out successfully', success: true });
    } catch (error) {
        next(error);
    }
};
