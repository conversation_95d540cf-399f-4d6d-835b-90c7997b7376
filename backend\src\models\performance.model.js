import mongoose from "mongoose";

const performanceSchema = new mongoose.Schema({
    employee: { 
        type: mongoose.Schema.Types.ObjectId, 
        ref: "Employee", 
        required: true 
    },
    goals: [{
        description: String,
        targetDate: Date,
        status: {
            type: String,
            enum: ["PENDING", "IN_PROGRESS", "COMPLETED"],
            default: "PENDING"
        }
    }],
    reviews: [{
        reviewer: { 
            type: mongoose.Schema.Types.ObjectId, 
            ref: "User" 
        },
        date: { 
            type: Date, 
            default: Date.now 
        },
        comments: String,
        rating: { 
            type: Number, 
            min: 1, 
            max: 5 
        }
    }]
}, { 
    timestamps: true 
});

export default mongoose.model("Performance", performanceSchema);
