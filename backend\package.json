{"name": "employee-management-system", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node --inspect --watch-path=./ ./server.js"}, "keywords": [], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "type": "module", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.18.0", "morgan": "^1.10.1"}}