// models/user.model.js
import mongoose from 'mongoose';

const userSchema = new mongoose.Schema(
    {
        username: {
            type: String,
            required: true,
            trim: true,
        },
        email: {
            type: String,
            required: true,
            unique: true,
            lowercase: true,
        },
        password: {
            type: String,
            required: true,
            select: false,
        },
        role: {
            type: String,
            enum: ['admin', 'hr', 'manager', 'employee'],
            default: 'employee',
        },
        refreshToken: {
            type: String,
            select: false,
        },
        lastLogin: {
            type: Date
        },
    },
    {
        timestamps: true,
    }
);

// Index for performance
userSchema.index({ email: 1 });
userSchema.index({ role: 1 });


const User = mongoose.model('User', userSchema);

export default User;
